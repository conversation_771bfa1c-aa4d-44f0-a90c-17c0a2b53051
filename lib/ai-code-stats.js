#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

/**
 * 深度合并配置对象
 */
function deepMerge(target, source) {
  const result = { ...target };

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
  }

  return result;
}

/**
 * 加载配置文件
 */
function loadConfig() {
  const configPath = path.join(process.cwd(), '.ai-code-stats.json');

  // 默认配置
  const defaultConfig = {
    "enabled": true,
    "autoCleanup": true,
    "verbose": true,
    "hooks": {
      "preCommit": true,
      "prePush": false
    },
    "api": {
      "enabled": true,
      "url": "https://connector.dingtalk.com/webhook/flow/1032e1dd14f40bbb57510005",
      "method": "POST",
      "headers": {
        "Content-Type": "application/json",
        "x-info": "pushcode"
      }
    },
    "ignore": {
      "files": [
        "package.json",
        "package-lock.json",
        "yarn.lock",
        "pnpm-lock.yaml",
        "npm-shrinkwrap.json"
      ],
      "patterns": [
        "*.log",
        "*.tmp",
        "*.md",
        "node_modules/**",
        ".git/**"
      ]
    }
  };

  if (fs.existsSync(configPath)) {
    try {
      const userConfig = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
      return deepMerge(defaultConfig, userConfig);
    } catch (error) {
      console.warn('⚠️  配置文件格式错误，使用默认配置');
      return defaultConfig;
    }
  }

  return defaultConfig;
}

// 加载配置
const CONFIG = loadConfig();

// 获取命令行参数
const args = process.argv.slice(2);
const commitHash = args.find(arg => !arg.startsWith('--')) || 'HEAD';
const outputMode = args.includes('--detailed') ? 'detailed' : 'summary';
const cleanupMode = args.includes('--no-cleanup') ? 'no-cleanup' : 'auto';
const isPreCommitMode = args.includes('--mode=pre-commit') || args.includes('--staged');
const isVerbose = !args.includes('--quiet') && CONFIG.verbose;

// 生成 commit review 链接
function genCommitLink(project) {
  return `https://code.alibaba-inc.com/${project}/commit/${commitHash}`;
}

/**
 * 获取仓库信息
 */
function getRepoInfo() {
  const repoUrl = execSync('git config --get remote.origin.url', { encoding: 'utf-8' }).trim();
  const branchName = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf-8' }).trim();
  let projectName = repoUrl.split(':')[1].replace('.git', '');

  if (repoUrl.startsWith('http')) {
    projectName =  repoUrl.replace('https://code.alibaba-inc.com/', '').replace('.git', '');
  }
  
  return { repoUrl, projectName, branchName, commitLink: genCommitLink(projectName) };
}

/**
 * 获取提交信息
 */
function getCommitInfo(hash) {
  const author = execSync(`git show -s --format="%an <%ae>" ${hash}`, { encoding: 'utf-8' }).trim();
  const message = execSync(`git show -s --format="%B" ${hash}`, { encoding: 'utf-8' }).trim();
  const timestamp = execSync(`git show -s --format="%at" ${hash}`, { encoding: 'utf-8' }).trim();
  const date = new Date(parseInt(timestamp) * 1000).toISOString();
  
  return { hash, author, message, date };
}

/**
 * 将glob模式转换为正则表达式
 */
function globToRegex(pattern) {
  // 转义正则表达式特殊字符，但保留通配符
  let regex = pattern
    .replace(/[.+^${}()|[\]\\]/g, '\\$&') // 转义特殊字符
    .replace(/\*\*/g, '___DOUBLESTAR___') // 临时替换 **
    .replace(/\*/g, '[^/]*') // * 匹配除 / 外的任意字符
    .replace(/___DOUBLESTAR___/g, '.*'); // ** 匹配任意字符包括 /

  return new RegExp(`^${regex}$`);
}

/**
 * 判断是否应该忽略某个文件的统计
 */
function shouldIgnoreFile(filePath) {
  const fileName = filePath.split('/').pop();

  // 检查忽略的文件列表
  if (CONFIG.ignore.files.includes(fileName)) {
    return true;
  }

  // 检查忽略的模式
  return CONFIG.ignore.patterns.some(pattern => {
    const regex = globToRegex(pattern);
    return regex.test(filePath);
  });
}

/**
 * 获取提交的代码统计信息
 */
function getCodeStats(hash) {
  try {
    // 获取总体变更统计
    const diffCommand = `git diff --numstat ${hash}^..${hash}`;
    const diffOutput = execSync(diffCommand, { encoding: 'utf-8' }).trim();

    if (!diffOutput) {
      return {
        files: 0,
        totalAdded: 0,
        totalDeleted: 0,
        netChange: 0,
        details: [],
        ignoredFiles: []
      };
    }

    const allStats = diffOutput
      .split('\n')
      .filter(Boolean)
      .map(line => {
        const [added, deleted, file] = line.split(/\s+/);
        return {
          file,
          added: isNaN(parseInt(added)) ? 0 : parseInt(added),
          deleted: isNaN(parseInt(deleted)) ? 0 : parseInt(deleted)
        };
      });

    // 分离需要统计的文件和被忽略的文件
    const stats = [];
    const ignoredFiles = [];

    allStats.forEach(stat => {
      if (shouldIgnoreFile(stat.file)) {
        ignoredFiles.push(stat);
      } else {
        stats.push(stat);
      }
    });

    let totalAdded = 0;
    let totalDeleted = 0;

    stats.forEach(stat => {
      totalAdded += stat.added;
      totalDeleted += stat.deleted;
    });

    return {
      files: stats.length,
      totalAdded,
      totalDeleted,
      netChange: totalAdded - totalDeleted,
      details: stats,
      ignoredFiles: ignoredFiles
    };
  } catch (error) {
    console.error(`获取代码统计出错: ${error.message}`);
    // 如果是首次提交，没有父提交可比较
    if (error.message.includes('fatal: bad revision')) {
      try {
        // 对于首次提交，统计所有添加的行
        const filesOutput = execSync(`git ls-tree -r ${hash} --name-only`, { encoding: 'utf-8' }).trim();
        const allFiles = filesOutput.split('\n').filter(Boolean);

        let totalAdded = 0;
        const details = [];
        const ignoredFiles = [];

        for (const file of allFiles) {
          try {
            const content = execSync(`git show ${hash}:${file}`, { encoding: 'utf-8' });
            const lines = content.split('\n').length;
            const fileStat = { file, added: lines, deleted: 0 };

            if (shouldIgnoreFile(file)) {
              ignoredFiles.push(fileStat);
            } else {
              details.push(fileStat);
              totalAdded += lines;
            }
          } catch (e) {
            // 忽略二进制文件等无法处理的文件
          }
        }

        return {
          files: details.length,
          totalAdded,
          totalDeleted: 0,
          netChange: totalAdded,
          details,
          ignoredFiles
        };
      } catch (e) {
        console.error(`处理首次提交统计出错: ${e.message}`);
      }
    }
    
    return {
      files: 0,
      totalAdded: 0,
      totalDeleted: 0,
      netChange: 0,
      details: [],
      ignoredFiles: []
    };
  }
}

/**
 * 获取staged文件列表
 */
function getStagedFiles() {
  try {
    const stagedFiles = execSync('git diff --cached --name-only', { encoding: 'utf-8' })
      .trim()
      .split('\n')
      .filter(Boolean);
    return stagedFiles;
  } catch (error) {
    console.error(`获取staged文件列表出错: ${error.message}`);
    return [];
  }
}

/**
 * 获取staged文件内容
 */
function getStagedFileContent(filePath) {
  try {
    return execSync(`git show :${filePath}`, { encoding: 'utf-8' });
  } catch (error) {
    // 如果文件是新添加的，可能在index中不存在，尝试读取工作目录文件
    try {
      return fs.readFileSync(filePath, 'utf-8');
    } catch (fsError) {
      throw new Error(`无法获取文件内容: ${error.message}`);
    }
  }
}

/**
 * 统计staged文件中的AI代码
 */
function getAICodeStatsFromStaged() {
  const stagedFiles = getStagedFiles();

  if (stagedFiles.length === 0) {
    return {
      filesWithAICode: 0,
      totalAILines: 0,
      details: []
    };
  }

  let totalAILines = 0;
  let filesWithAICode = 0;
  const aiCodeDetails = [];

  stagedFiles.forEach(filePath => {
    // 跳过包管理文件
    if (shouldIgnoreFile(filePath)) {
      return;
    }

    try {
      const fileContent = getStagedFileContent(filePath);

      // 计算AI生成的代码行数
      let aiLinesInFile = 0;
      let isInsideAIBlock = false;
      let currentBlock = [];

      const lines = fileContent.split('\n');

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        if (line.includes('[[GBAI START]]')) {
          isInsideAIBlock = true;
          currentBlock = [];
        } else if (line.includes('[[GBAI END]]')) {
          isInsideAIBlock = false;
          aiLinesInFile += currentBlock.length;
        } else if (isInsideAIBlock) {
          currentBlock.push(line);
        }
      }

      if (aiLinesInFile > 0) {
        aiCodeDetails.push({ file: filePath, aiLines: aiLinesInFile });
        totalAILines += aiLinesInFile;
        filesWithAICode++;
      }
    } catch (error) {
      if (isVerbose) {
        console.error(`无法处理staged文件 ${filePath}: ${error.message}`);
      }
    }
  });

  return {
    filesWithAICode,
    totalAILines,
    details: aiCodeDetails
  };
}

/**
 * 统计AI生成的代码
 */
function getAICodeStats(hash) {
  // 获取指定提交中修改的文件列表
  let changedFiles = [];
  try {
    changedFiles = execSync(`git diff-tree --no-commit-id --name-only -r ${hash}`, { encoding: 'utf-8' })
      .trim()
      .split('\n')
      .filter(Boolean);
  } catch (error) {
    console.error(`获取修改文件列表出错: ${error.message}`);
    return {
      filesWithAICode: 0,
      totalAILines: 0,
      details: []
    };
  }
  
  let totalAILines = 0;
  let filesWithAICode = 0;
  const aiCodeDetails = [];

  // 检查每个文件中的AI生成代码（排除包管理文件）
  changedFiles.forEach(filePath => {
    // 跳过包管理文件
    if (shouldIgnoreFile(filePath)) {
      return;
    }

    try {
      // 获取该提交时的文件内容
      const fileContent = execSync(`git show ${hash}:${filePath}`, { encoding: 'utf-8' });
      
      // 计算AI生成的代码行数
      const aiCodeBlocks = [];
      let isInsideAIBlock = false;
      let currentBlock = [];
      let aiLinesInFile = 0;

      const lines = fileContent.split('\n');
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        if (line.includes('[[GBAI START]]')) {
          isInsideAIBlock = true;
          currentBlock = [];
        } else if (line.includes('[[GBAI END]]')) {
          isInsideAIBlock = false;
          aiCodeBlocks.push(currentBlock);
          aiLinesInFile += currentBlock.length;
        } else if (isInsideAIBlock) {
          currentBlock.push(line);
        }
      }

      if (aiLinesInFile > 0) {
        aiCodeDetails.push({ file: filePath, aiLines: aiLinesInFile });
        totalAILines += aiLinesInFile;
        filesWithAICode++;
      }
    } catch (error) {
      if (CONFIG.verbose) {
        console.error(`无法处理文件 ${filePath}: ${error.message}`);
      }
    }
  });

  return {
    filesWithAICode,
    totalAILines,
    details: aiCodeDetails
  };
}

/**
 * 获取所有需要检查的文件
 */
function getAllFilesToCheck() {
  // 构建find命令的排除条件
  let findCommand = 'find . -type f';

  // 添加基本排除条件
  findCommand += ' -not -path "./.git/*"';

  // 根据配置添加排除条件
  CONFIG.ignore.patterns.forEach(pattern => {
    if (pattern.includes('**')) {
      // 处理 ** 模式
      const pathPattern = pattern.replace('**', '*');
      findCommand += ` -not -path "./${pathPattern}"`;
    } else if (pattern.startsWith('*.')) {
      // 处理文件扩展名模式
      findCommand += ` -not -name "${pattern}"`;
    } else {
      // 处理其他模式
      findCommand += ` -not -path "./${pattern}"`;
    }
  });

  return findCommand;
}

/**
 * 清理工作目录中的AI标记
 */
function cleanupAIMarkers() {
  try {
    console.log('🔍 开始扫描文件...');

    // 获取当前工作目录中的所有文件
    const findCommand = getAllFilesToCheck();
    if (CONFIG.verbose) {
      console.log(`📂 执行命令: ${findCommand}`);
    }

    const findOutput = execSync(findCommand, { encoding: 'utf-8' });
    if (CONFIG.verbose) {
      console.log(`📄 find命令执行完成，输出长度: ${findOutput.length} 字符`);
    }

    const allFiles = findOutput
      .trim()
      .split('\n')
      .filter(Boolean)
      .filter(filePath => !shouldIgnoreFile(filePath.replace('./', ''))); // 额外过滤

    console.log(`📋 找到 ${allFiles.length} 个文件需要检查`);

    let cleanedFiles = 0;
    let totalMarkersRemoved = 0;
    let processedCount = 0;

    allFiles.forEach((filePath) => {
      processedCount++;

      // 每处理100个文件输出一次进度
      if (processedCount % 100 === 0 || processedCount === allFiles.length) {
        console.log(`⏳ 处理进度: ${processedCount}/${allFiles.length} (${(processedCount/allFiles.length*100).toFixed(1)}%)`);
      }

      try {
        if (CONFIG.verbose) {
          console.log(`🔍 检查文件: ${filePath}`);
        }

        // 读取文件内容
        const content = fs.readFileSync(filePath, 'utf-8');
        if (CONFIG.verbose) {
          console.log(`📖 成功读取文件: ${filePath}, 大小: ${content.length} 字符`);
        }

        // 检查是否包含AI标记
        const hasStarMarker = content.includes('[[GBAI START]]');
        const hasEndMarker = content.includes('[[GBAI END]]');

        if (hasStarMarker || hasEndMarker) {
          console.log(`🎯 发现AI标记在文件: ${filePath}`);

          // 清理AI标记
          let cleanedContent = content;
          let markersInFile = 0;

          // 计算并移除标记
          const starMatches = content.match(/.*\[\[GBAI START\]\].*/g) || [];
          const endMatches = content.match(/.*\[\[GBAI END\]\].*/g) || [];
          markersInFile = starMatches.length + endMatches.length;

          if (CONFIG.verbose) {
            console.log(`🧹 准备清理 ${markersInFile} 个标记 (START: ${starMatches.length}, END: ${endMatches.length})`);
          }

          // 移除包含标记的整行
          cleanedContent = cleanedContent
            .split('\n')
            .filter(line => !line.includes('[[GBAI START]]') && !line.includes('[[GBAI END]]'))
            .join('\n');

          if (CONFIG.verbose) {
            console.log(`💾 准备写回文件: ${filePath}`);
          }
          // 写回文件
          fs.writeFileSync(filePath, cleanedContent, 'utf-8');

          if (markersInFile > 0) {
            cleanedFiles++;
            totalMarkersRemoved += markersInFile;
            console.log(`✅ 清理完成: ${filePath} (移除 ${markersInFile} 个标记)`);
          }
        }
      } catch (error) {
        console.error(`❌ 处理文件出错: ${filePath}, 错误: ${error.message}`);
        // 忽略无法处理的文件（如二进制文件）
        if (CONFIG.verbose && !error.message.includes('EISDIR')) {
          console.error(`  跳过文件 ${filePath}: ${error.message}`);
        }
      }
    });

    console.log(`🏁 文件扫描完成！`);
    console.log(`📊 统计结果: 清理了 ${cleanedFiles} 个文件中的 ${totalMarkersRemoved} 个AI标记`);

    return { cleanedFiles, totalMarkersRemoved };
  } catch (error) {
    console.error(`❌ 清理AI标记时出错: ${error.message}`);
    console.error(`🔍 错误堆栈: ${error.stack}`);
    return { cleanedFiles: 0, totalMarkersRemoved: 0 };
  }
}

/**
 * 向远程API发送统计数据
 */
function sendStatsToAPI(data) {
  return new Promise((resolve, reject) => {
    const apiUrl = new URL(CONFIG.api.url);
    const isHttps = apiUrl.protocol === 'https:';
    const httpModule = isHttps ? https : http;

    const requestOptions = {
      hostname: apiUrl.hostname,
      port: apiUrl.port || (isHttps ? 443 : 80),
      path: apiUrl.pathname + apiUrl.search,
      method: CONFIG.api.method,
      headers: CONFIG.api.headers
    };

    const req = httpModule.request(requestOptions, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve({ statusCode: res.statusCode, data: responseData });
        } else {
          reject(new Error(`API请求失败: ${res.statusCode} ${responseData}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.write(JSON.stringify(data));
    req.end();
  });
}

/**
 * 清理staged文件并重新add
 */
function cleanupStagedFiles() {
  const stagedFiles = getStagedFiles();

  if (stagedFiles.length === 0) {
    return { cleanedFiles: 0, totalMarkersRemoved: 0 };
  }

  let cleanedFiles = 0;
  let totalMarkersRemoved = 0;

  stagedFiles.forEach(filePath => {
    // 跳过包管理文件和不存在的文件
    if (shouldIgnoreFile(filePath) || !fs.existsSync(filePath)) {
      return;
    }

    try {
      const content = fs.readFileSync(filePath, 'utf-8');

      if (content.includes('[[GBAI START]]') || content.includes('[[GBAI END]]')) {
        // 计算标记数量
        const starMatches = content.match(/.*\[\[GBAI START\]\].*/g) || [];
        const endMatches = content.match(/.*\[\[GBAI END\]\].*/g) || [];
        const markersInFile = starMatches.length + endMatches.length;

        // 清理标记
        const cleanedContent = content
          .split('\n')
          .filter(line => !line.includes('[[GBAI START]]') && !line.includes('[[GBAI END]]'))
          .join('\n');

        // 写回文件
        fs.writeFileSync(filePath, cleanedContent, 'utf-8');

        // 重新add文件
        execSync(`git add "${filePath}"`);

        cleanedFiles++;
        totalMarkersRemoved += markersInFile;

        if (isVerbose) {
          console.log(`✓ ${filePath} (移除 ${markersInFile} 个标记)`);
        }
      }
    } catch (error) {
      if (isVerbose) {
        console.error(`处理文件出错 ${filePath}: ${error.message}`);
      }
    }
  });

  return { cleanedFiles, totalMarkersRemoved };
}

/**
 * 主函数
 */
async function main() {
  try {
    let repoInfo, commitInfo, codeStats, aiCodeStats;

    if (isPreCommitMode) {
      // Pre-commit模式：统计staged文件
      repoInfo = getRepoInfo();
      aiCodeStats = getAICodeStatsFromStaged();

      // 为pre-commit模式创建虚拟的commit和code stats
      commitInfo = {
        hash: 'staged',
        author: 'Current User',
        message: 'Staged changes',
        date: new Date().toISOString()
      };

      // 计算staged文件的代码统计
      const stagedFiles = getStagedFiles();
      let totalAdded = 0;
      stagedFiles.forEach(filePath => {
        if (!shouldIgnoreFile(filePath) && fs.existsSync(filePath)) {
          try {
            const content = getStagedFileContent(filePath);
            totalAdded += content.split('\n').length;
          } catch (error) {
            // 忽略无法处理的文件
          }
        }
      });

      codeStats = {
        files: stagedFiles.length,
        totalAdded,
        totalDeleted: 0,
        netChange: totalAdded,
        details: [],
        ignoredFiles: []
      };
    } else {
      // 传统模式：统计指定commit
      repoInfo = getRepoInfo();
      commitInfo = getCommitInfo(commitHash);
      codeStats = getCodeStats(commitHash);
      aiCodeStats = getAICodeStats(commitHash);
    }
    
    // 组合所有统计数据
    const statsData = {
      repository: repoInfo,
      commit: commitInfo,
      codeChanges: codeStats,
      aiCode: aiCodeStats,
      timestamp: new Date().toISOString(),
      event: 'pushcode' // 标记这是push事件触发的统计
    };
    
    // 输出统计信息到控制台
    if (aiCodeStats.totalAILines > 0) {
      const aiPercentage = codeStats.totalAdded > 0 ? (aiCodeStats.totalAILines / codeStats.totalAdded * 100).toFixed(1) : '0';
      console.log(`AI代码: ${aiCodeStats.totalAILines}行 (${aiPercentage}%) 在${aiCodeStats.filesWithAICode}个文件中`);
    } else {
      console.log(`AI代码: 无`);
    }

    if (CONFIG.verbose) {
      if (outputMode === 'summary') {
        // 简洁模式：只输出关键信息
        console.log(`[${commitInfo.hash.substring(0, 8)}] ${commitInfo.message.split('\n')[0]}`);
        // console.log(`  代码变更: +${codeStats.totalAdded}/-${codeStats.totalDeleted} (${codeStats.files}个文件)`);
      } else {
        // 详细模式：完整的统计报告
        console.log('\n===== 代码提交统计 =====');
        console.log('AI生成代码:');
        console.log(`- 包含AI代码的文件数: ${aiCodeStats.filesWithAICode}`);
        console.log(`- AI生成的总代码行数: ${aiCodeStats.totalAILines}`);

        // 避免除以零
        if (codeStats.totalAdded > 0) {
          console.log(`- AI代码占比: ${(aiCodeStats.totalAILines / codeStats.totalAdded * 100).toFixed(2)}%`);
        }
      }
    }
    
    // 发送统计数据到远程API
    if (CONFIG.api.enabled) {
      try {
        await sendStatsToAPI(statsData);
        if (CONFIG.verbose) {
          console.log('\n统计数据已成功发送到远程API');
        }
      } catch (apiError) {
        console.error('\n发送统计数据失败:', apiError.message);
      }
    }

    // 清理AI标记
    if (isPreCommitMode) {
      // Pre-commit模式：清理staged文件并重新add
      if (aiCodeStats.totalAILines > 0 && cleanupMode !== 'no-cleanup') {
        if (isVerbose) {
          console.log('\n===== 清理AI标记 =====');
        }

        const cleanupResult = cleanupStagedFiles();

        if (isVerbose) {
          if (cleanupResult.totalMarkersRemoved > 0) {
            console.log(`✓ 已清理 ${cleanupResult.cleanedFiles} 个文件中的 ${cleanupResult.totalMarkersRemoved} 个AI标记`);
          } else {
            console.log('! 没有发现需要清理的AI标记');
          }
        }
      }
    } else {
      // 传统模式：使用原有的清理逻辑
      if (CONFIG.autoCleanup && cleanupMode !== 'no-cleanup') {
        if (isVerbose) {
          console.log('🚀 开始执行AI标记清理流程...');
        }

        if (isVerbose && outputMode === 'detailed') {
          console.log('\n===== 清理AI标记 =====');
        }

        const cleanupResult = cleanupAIMarkers();

        if (isVerbose) {
          if (outputMode === 'detailed') {
            if (cleanupResult.totalMarkersRemoved > 0) {
              console.log(`已清理 ${cleanupResult.cleanedFiles} 个文件中的 ${cleanupResult.totalMarkersRemoved} 个AI标记`);
            } else {
              console.log('没有发现需要清理的AI标记');
            }
          } else if (outputMode === 'summary' && cleanupResult.totalMarkersRemoved > 0) {
            console.log(`  清理: 移除了 ${cleanupResult.totalMarkersRemoved} 个AI标记`);
          }
        }
      }
    }

  } catch (error) {
    console.error(`执行出错: ${error.message}`);
    process.exit(1);
  }
}

// 执行主函数
main();
